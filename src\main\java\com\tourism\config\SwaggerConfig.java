package com.tourism.config;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.oas.annotations.EnableOpenApi;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

/**
 * Swagger配置类，集成Knife4j增强
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Configuration
@EnableOpenApi
@EnableKnife4j
public class SwaggerConfig {

    /**
     * 创建API文档
     *
     * @return Docket实例
     */
    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.OAS_30)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.tourism.controller"))
                .paths(PathSelectors.any())
                .build()
                .enable(true)
                .forCodeGeneration(true);
    }

    /**
     * API信息
     *
     * @return ApiInfo实例
     */
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("旅游讲解小程序文件管理API")
                .description("基于腾讯云COS的图像和视频文件上传下载管理系统")
                .version("1.0.0")
                .contact(new Contact("Tourism Team", "https://tourism.com", "<EMAIL>"))
                .license("Apache License 2.0")
                .licenseUrl("https://www.apache.org/licenses/LICENSE-2.0")
                .build();
    }
}
