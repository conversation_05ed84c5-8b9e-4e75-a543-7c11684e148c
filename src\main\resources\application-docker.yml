server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: tourism-file-management-api
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# 腾讯云COS配置（Docker环境）
tencent:
  cos:
    secret-id: ${TENCENT_COS_SECRET_ID}
    secret-key: ${TENCENT_COS_SECRET_KEY}
    region: ${TENCENT_COS_REGION}
    bucket-name: ${TENCENT_COS_BUCKET_NAME}
    cdn-domain: ${TENCENT_COS_CDN_DOMAIN:}
    url-expire-time: ${TENCENT_COS_URL_EXPIRE_TIME:3600}

# Swagger配置
springfox:
  documentation:
    swagger-ui:
      enabled: true

# Knife4j配置
knife4j:
  enable: true
  setting:
    language: zh_cn

# 日志配置（Docker环境）
logging:
  level:
    root: INFO
    com.tourism: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/tourism-file-api.log

# 文件上传配置
file:
  upload:
    image-types: ${FILE_UPLOAD_IMAGE_TYPES:jpg,jpeg,png,gif,bmp,webp}
    video-types: ${FILE_UPLOAD_VIDEO_TYPES:mp4,avi,mov,wmv,flv,3gp,mkv}
    max-size: ${FILE_UPLOAD_MAX_SIZE:104857600}
    path-prefix: ${FILE_UPLOAD_PATH_PREFIX:tourism/files/}
