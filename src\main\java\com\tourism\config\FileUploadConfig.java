package com.tourism.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

/**
 * 文件上传配置类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "file.upload")
public class FileUploadConfig {

    /**
     * 允许的图片格式
     */
    private String imageTypes = "jpg,jpeg,png,gif,bmp,webp";

    /**
     * 允许的视频格式
     */
    private String videoTypes = "mp4,avi,mov,wmv,flv,3gp,mkv";

    /**
     * 最大文件大小（字节）
     */
    private Long maxSize = 104857600L; // 100MB

    /**
     * 文件存储路径前缀
     */
    private String pathPrefix = "tourism/files/";

    /**
     * 获取允许的图片格式列表
     * 
     * @return 图片格式列表
     */
    public List<String> getImageTypeList() {
        return Arrays.asList(imageTypes.toLowerCase().split(","));
    }

    /**
     * 获取允许的视频格式列表
     * 
     * @return 视频格式列表
     */
    public List<String> getVideoTypeList() {
        return Arrays.asList(videoTypes.toLowerCase().split(","));
    }

    /**
     * 检查是否为允许的图片格式
     * 
     * @param fileExtension 文件扩展名
     * @return 是否允许
     */
    public boolean isAllowedImageType(String fileExtension) {
        return getImageTypeList().contains(fileExtension.toLowerCase());
    }

    /**
     * 检查是否为允许的视频格式
     * 
     * @param fileExtension 文件扩展名
     * @return 是否允许
     */
    public boolean isAllowedVideoType(String fileExtension) {
        return getVideoTypeList().contains(fileExtension.toLowerCase());
    }

    /**
     * 检查是否为允许的文件格式
     * 
     * @param fileExtension 文件扩展名
     * @return 是否允许
     */
    public boolean isAllowedFileType(String fileExtension) {
        return isAllowedImageType(fileExtension) || isAllowedVideoType(fileExtension);
    }
}
