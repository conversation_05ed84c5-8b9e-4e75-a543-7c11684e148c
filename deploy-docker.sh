#!/bin/bash

echo "=== 旅游讲解小程序文件上传API Docker部署脚本 ==="
echo ""

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查环境变量文件
if [ ! -f ".env.prod" ]; then
    echo "❌ 未找到.env.prod文件，请先配置环境变量"
    echo "请复制.env.prod.example并修改配置"
    exit 1
fi

echo "1. 停止现有服务..."
docker-compose -f docker-compose.prod.yml --env-file .env.prod down

echo ""
echo "2. 清理旧镜像..."
docker image prune -f

echo ""
echo "3. 构建JAR包..."
mvn clean package -DskipTests

echo ""
echo "4. 构建Docker镜像..."
docker-compose -f docker-compose.prod.yml --env-file .env.prod build --no-cache

echo ""
echo "5. 启动服务..."
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d

echo ""
echo "6. 等待服务启动..."
sleep 30

echo ""
echo "7. 检查服务状态..."
docker-compose -f docker-compose.prod.yml ps

echo ""
echo "8. 检查健康状态..."
curl -f http://localhost:8080/api/health/check || echo "健康检查失败，请查看日志"

echo ""
echo "9. 显示最近日志..."
docker-compose -f docker-compose.prod.yml logs --tail=50

echo ""
echo "=== 部署完成 ==="
echo "API文档地址: http://your-server-ip:8080/api/doc.html"
echo "健康检查: http://your-server-ip:8080/api/health/check"
echo ""
echo "常用命令:"
echo "查看日志: docker-compose -f docker-compose.prod.yml logs -f"
echo "重启服务: docker-compose -f docker-compose.prod.yml restart"
echo "停止服务: docker-compose -f docker-compose.prod.yml down"
