package com.tourism.config;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.region.Region;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 腾讯云COS配置类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "tencent.cos")
public class TencentCosConfig {

    /**
     * 腾讯云API密钥ID
     */
    private String secretId;

    /**
     * 腾讯云API密钥Key
     */
    private String secretKey;

    /**
     * COS存储桶所在地域
     */
    private String region;

    /**
     * COS存储桶名称
     */
    private String bucketName;

    /**
     * CDN加速域名（可选）
     */
    private String cdnDomain;

    /**
     * 文件访问URL有效期（秒）
     */
    private Long urlExpireTime = 3600L;

    /**
     * 创建COS客户端Bean
     * 
     * @return COSClient实例
     */
    @Bean
    public COSClient cosClient() {
        // 1. 初始化用户身份信息（secretId, secretKey）
        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
        
        // 2. 设置bucket的地域
        Region regionObj = new Region(region);
        ClientConfig clientConfig = new ClientConfig(regionObj);
        
        // 3. 生成cos客户端
        return new COSClient(cred, clientConfig);
    }
}
