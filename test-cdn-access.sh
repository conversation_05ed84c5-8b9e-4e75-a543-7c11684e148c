#!/bin/bash

echo "=== CDN访问测试脚本 ==="
echo ""

# 配置信息
CDN_DOMAIN="https://www.gobsweb.com"
COS_DOMAIN="https://travel-1315014578.cos.ap-guangzhou.myqcloud.com"

echo "1. 测试CDN域名连通性..."
echo "CDN域名: $CDN_DOMAIN"
curl -I "$CDN_DOMAIN" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ CDN域名可访问"
else
    echo "❌ CDN域名无法访问"
fi
echo ""

echo "2. 测试COS源站连通性..."
echo "COS域名: $COS_DOMAIN"
curl -I "$COS_DOMAIN" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ COS源站可访问"
else
    echo "❌ COS源站无法访问"
fi
echo ""

echo "3. DNS解析检查..."
echo "CDN域名解析:"
nslookup www.gobsweb.com
echo ""

echo "4. 测试文件上传并访问..."
echo "请先上传一个测试文件，然后使用返回的URL进行测试"
echo ""

echo "5. 常见问题检查清单:"
echo "□ CDN回源配置是否正确指向COS"
echo "□ COS存储桶权限是否为公有读"
echo "□ CDN缓存是否需要刷新"
echo "□ 文件路径是否正确"
echo "□ HTTPS证书是否配置正确"
