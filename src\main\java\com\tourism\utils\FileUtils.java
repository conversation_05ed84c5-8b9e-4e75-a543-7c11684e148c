package com.tourism.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.digest.DigestUtil;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Date;

/**
 * 文件工具类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class FileUtils {

    /**
     * 获取文件扩展名
     * 
     * @param fileName 文件名
     * @return 文件扩展名（不包含点号）
     */
    public static String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        return fileName.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * 生成存储文件名
     * 
     * @param originalFileName 原始文件名
     * @return 存储文件名
     */
    public static String generateStoredFileName(String originalFileName) {
        String extension = getFileExtension(originalFileName);
        String dateStr = DateUtil.format(new Date(), "yyyyMMdd_HHmmss");
        String uuid = IdUtil.simpleUUID().substring(0, 8);
        
        if (extension.isEmpty()) {
            return dateStr + "_" + uuid;
        } else {
            return dateStr + "_" + uuid + "." + extension;
        }
    }

    /**
     * 判断文件类型
     * 
     * @param fileName 文件名
     * @return 文件类型（image/video/other）
     */
    public static String getFileType(String fileName) {
        String extension = getFileExtension(fileName);
        
        // 图片格式
        String[] imageExtensions = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};
        for (String ext : imageExtensions) {
            if (ext.equals(extension)) {
                return "image";
            }
        }
        
        // 视频格式
        String[] videoExtensions = {"mp4", "avi", "mov", "wmv", "flv", "3gp", "mkv"};
        for (String ext : videoExtensions) {
            if (ext.equals(extension)) {
                return "video";
            }
        }
        
        return "other";
    }

    /**
     * 计算文件MD5值
     * 
     * @param file 文件
     * @return MD5值
     * @throws IOException IO异常
     */
    public static String calculateMD5(MultipartFile file) throws IOException {
        return DigestUtil.md5Hex(file.getInputStream());
    }

    /**
     * 格式化文件大小
     * 
     * @param size 文件大小（字节）
     * @return 格式化后的文件大小
     */
    public static String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 生成文件存储路径
     * 
     * @param pathPrefix 路径前缀
     * @param fileType 文件类型
     * @param storedFileName 存储文件名
     * @return 完整存储路径
     */
    public static String generateFilePath(String pathPrefix, String fileType, String storedFileName) {
        String dateFolder = DateUtil.format(new Date(), "yyyy/MM/dd");
        return pathPrefix + fileType + "/" + dateFolder + "/" + storedFileName;
    }

    /**
     * 验证文件是否为空
     * 
     * @param file 文件
     * @return 是否为空
     */
    public static boolean isEmpty(MultipartFile file) {
        return file == null || file.isEmpty() || file.getSize() == 0;
    }

    /**
     * 验证文件大小
     * 
     * @param file 文件
     * @param maxSize 最大允许大小（字节）
     * @return 是否超出限制
     */
    public static boolean isFileSizeExceeded(MultipartFile file, long maxSize) {
        return file.getSize() > maxSize;
    }
}
