# 使用OpenJDK 8作为基础镜像
FROM openjdk:8-jre-alpine

# 设置维护者信息
LABEL maintainer="Tourism Team <<EMAIL>>"
LABEL description="旅游讲解小程序文件管理API系统"
LABEL version="1.0.0"

# 设置工作目录
WORKDIR /app

# 设置时区
RUN apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    apk del tzdata

# 创建应用用户
RUN addgroup -g 1000 tourism && \
    adduser -D -s /bin/sh -u 1000 -G tourism tourism

# 创建日志目录
RUN mkdir -p /app/logs && \
    chown -R tourism:tourism /app

# 复制JAR文件
COPY target/file-management-api-1.0.0.jar app.jar

# 设置文件权限
RUN chown tourism:tourism app.jar

# 切换到应用用户
USER tourism

# 暴露端口
EXPOSE 8080

# 设置JVM参数
ENV JAVA_OPTS="-Xms512m -Xmx1024m -Dfile.encoding=UTF-8 -Duser.timezone=GMT+08"

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/api/health/check || exit 1

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
