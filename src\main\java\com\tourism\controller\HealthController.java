package com.tourism.controller;

import com.tourism.common.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/health")
@Api(tags = "健康检查接口", description = "提供系统健康状态检查")
public class HealthController {

    /**
     * 健康检查
     * 
     * @return 健康状态
     */
    @GetMapping("/check")
    @ApiOperation(value = "健康检查", notes = "检查系统是否正常运行")
    public Result<Map<String, Object>> healthCheck() {
        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("status", "UP");
        healthInfo.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        healthInfo.put("service", "Tourism File Management API");
        healthInfo.put("version", "1.0.0");
        
        return Result.success("系统运行正常", healthInfo);
    }

    /**
     * 获取系统信息
     * 
     * @return 系统信息
     */
    @GetMapping("/info")
    @ApiOperation(value = "获取系统信息", notes = "获取系统基本信息")
    public Result<Map<String, Object>> getSystemInfo() {
        Map<String, Object> systemInfo = new HashMap<>();
        
        // JVM信息
        Runtime runtime = Runtime.getRuntime();
        systemInfo.put("jvm.version", System.getProperty("java.version"));
        systemInfo.put("jvm.vendor", System.getProperty("java.vendor"));
        systemInfo.put("jvm.totalMemory", runtime.totalMemory() / 1024 / 1024 + " MB");
        systemInfo.put("jvm.freeMemory", runtime.freeMemory() / 1024 / 1024 + " MB");
        systemInfo.put("jvm.maxMemory", runtime.maxMemory() / 1024 / 1024 + " MB");
        
        // 系统信息
        systemInfo.put("os.name", System.getProperty("os.name"));
        systemInfo.put("os.version", System.getProperty("os.version"));
        systemInfo.put("os.arch", System.getProperty("os.arch"));
        
        // 应用信息
        systemInfo.put("app.name", "Tourism File Management API");
        systemInfo.put("app.version", "1.0.0");
        systemInfo.put("app.description", "旅游讲解小程序文件管理API系统");
        
        return Result.success("获取系统信息成功", systemInfo);
    }
}
