{"groups": [{"name": "file.upload", "type": "com.tourism.config.FileUploadConfig", "sourceType": "com.tourism.config.FileUploadConfig"}, {"name": "tencent.cos", "type": "com.tourism.config.TencentCosConfig", "sourceType": "com.tourism.config.TencentCosConfig"}], "properties": [{"name": "file.upload.image-type-list", "type": "java.util.List<java.lang.String>", "sourceType": "com.tourism.config.FileUploadConfig"}, {"name": "file.upload.image-types", "type": "java.lang.String", "description": "允许的图片格式", "sourceType": "com.tourism.config.FileUploadConfig"}, {"name": "file.upload.max-size", "type": "java.lang.Long", "description": "最大文件大小（字节）", "sourceType": "com.tourism.config.FileUploadConfig"}, {"name": "file.upload.path-prefix", "type": "java.lang.String", "description": "文件存储路径前缀", "sourceType": "com.tourism.config.FileUploadConfig"}, {"name": "file.upload.video-type-list", "type": "java.util.List<java.lang.String>", "sourceType": "com.tourism.config.FileUploadConfig"}, {"name": "file.upload.video-types", "type": "java.lang.String", "description": "允许的视频格式", "sourceType": "com.tourism.config.FileUploadConfig"}, {"name": "tencent.cos.bucket-name", "type": "java.lang.String", "description": "COS存储桶名称", "sourceType": "com.tourism.config.TencentCosConfig"}, {"name": "tencent.cos.cdn-domain", "type": "java.lang.String", "description": "CDN加速域名（可选）", "sourceType": "com.tourism.config.TencentCosConfig"}, {"name": "tencent.cos.region", "type": "java.lang.String", "description": "COS存储桶所在地域", "sourceType": "com.tourism.config.TencentCosConfig"}, {"name": "tencent.cos.secret-id", "type": "java.lang.String", "description": "腾讯云API密钥ID", "sourceType": "com.tourism.config.TencentCosConfig"}, {"name": "tencent.cos.secret-key", "type": "java.lang.String", "description": "腾讯云API密钥Key", "sourceType": "com.tourism.config.TencentCosConfig"}, {"name": "tencent.cos.url-expire-time", "type": "java.lang.Long", "description": "文件访问URL有效期（秒）", "sourceType": "com.tourism.config.TencentCosConfig"}], "hints": []}